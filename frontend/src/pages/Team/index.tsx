import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Avatar, Typography, Tabs, Badge, Spin, Alert, message, Modal, Space, Divider } from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  TeamOutlined,
  ExperimentOutlined,
  GlobalOutlined,
  LoadingOutlined,
  BookOutlined,
  IdcardOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { BlocksRenderer, type BlocksContent } from '@strapi/blocks-react-renderer';
import type { TeamMember, StrapiBlocks } from '../../types';
import TeamMemberService from '../../api/teamMember';

const { Title, Paragraph } = Typography;

// 动态生成滚动条样式的函数
const getScrollbarStyles = () => {
  // 获取当前主题的CSS变量值
  const getThemeColor = (variable: string, fallback: string) => {
    if (typeof window !== 'undefined' && document.documentElement) {
      const value = getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
      return value || fallback;
    }
    return fallback;
  };

  const scrollbarThumb = getThemeColor('--border-tertiary', '#d1d5db');
  const scrollbarThumbHover = getThemeColor('--text-tertiary', '#9ca3af');
  const scrollbarTrack = getThemeColor('--bg-tertiary', '#f3f4f6');

  return `
    .research-scrollable::-webkit-scrollbar {
      width: 4px;
    }
    .research-scrollable::-webkit-scrollbar-track {
      background: transparent;
    }
    .research-scrollable::-webkit-scrollbar-thumb {
      background: ${scrollbarThumb};
      border-radius: 2px;
    }
    .research-scrollable::-webkit-scrollbar-thumb:hover {
      background: ${scrollbarThumbHover};
    }

    .modal-research-scrollable::-webkit-scrollbar {
      width: 6px;
    }
    .modal-research-scrollable::-webkit-scrollbar-track {
      background: ${scrollbarTrack};
      border-radius: 3px;
    }
    .modal-research-scrollable::-webkit-scrollbar-thumb {
      background: ${scrollbarThumb};
      border-radius: 3px;
    }
    .modal-research-scrollable::-webkit-scrollbar-thumb:hover {
      background: ${scrollbarThumbHover};
    }

    /* Firefox 滚动条样式 */
    .research-scrollable {
      scrollbar-width: thin;
      scrollbar-color: ${scrollbarThumb} transparent;
    }

    .modal-research-scrollable {
      scrollbar-width: thin;
      scrollbar-color: ${scrollbarThumb} ${scrollbarTrack};
    }
  `;
};

const Team: React.FC = () => {
  // 注入全局滚动条样式
  React.useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = getScrollbarStyles();
    document.head.appendChild(styleElement);

    // 监听主题变化，更新滚动条样式
    const updateScrollbarStyles = () => {
      styleElement.textContent = getScrollbarStyles();
    };

    const observer = new MutationObserver(updateScrollbarStyles);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });

    return () => {
      observer.disconnect();
      if (document.head.contains(styleElement)) {
        document.head.removeChild(styleElement);
      }
    };
  }, []);

  // 状态管理
  const [teamMembers, setTeamMembers] = useState<Record<string, TeamMember[]>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // Mentor 详情弹窗状态
  const [selectedMentor, setSelectedMentor] = useState<TeamMember | null>(null);
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  // 获取团队成员数据
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        setLoading(true);
        setError(null);

        // 定义角色列表
        const roles: Array<'Mentor' | 'Collaborator' | 'RA' | 'Alumni' | 'PhD' | 'Master' | 'Bachelor'> =
          ['Mentor', 'Collaborator', 'RA', 'Alumni', 'PhD', 'Master', 'Bachelor'];

        // 并行获取所有角色的成员数据
        const memberPromises = roles.map(async (role) => {
          try {
            const members = await TeamMemberService.getTeamMembersByRole(role);
            return { role, members };
          } catch (error) {
            console.error(`获取${role}角色成员失败:`, error);
            if (error.response) {
              console.error(`${role} API 错误状态:`, error.response.status);
              console.error(`${role} API 错误数据:`, error.response.data);
            }
            return { role, members: [] };
          }
        });

        const results = await Promise.all(memberPromises);

        // 组织数据结构
        const organizedMembers: Record<string, TeamMember[]> = {};
        results.forEach(({ role, members }) => {
          organizedMembers[role] = members;
        });

        setTeamMembers(organizedMembers);
      } catch (error) {
        console.error('获取团队成员数据失败:', error);
        setError('获取团队成员数据失败，请稍后重试');
        message.error('获取团队成员数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  // 使用官方 Strapi BlocksRenderer 渲染内容的组件
  const StrapiBlocksContent: React.FC<{
    content: StrapiBlocks | undefined;
    style?: React.CSSProperties;
    compact?: boolean;
  }> = ({ content, style, compact = false }) => {
    if (!content || !Array.isArray(content)) {
      return <span style={{ color: 'var(--text-secondary)', fontStyle: 'italic' }}>暂无信息</span>;
    }

    // 将 StrapiBlocks 转换为 BlocksContent 格式
    const blocksContent = content as unknown as BlocksContent;

    const defaultStyle = {
      fontSize: compact ? '13px' : '15px',
      color: compact ? 'var(--text-secondary)' : 'var(--text-primary)',
      lineHeight: '1.6'
    };

    const combinedStyle = { ...defaultStyle, ...style };

    return (
      <div style={combinedStyle}>
        <BlocksRenderer
          content={blocksContent}
          blocks={{
            // 自定义段落样式
            paragraph: ({ children }) => (
              <p style={{
                margin: compact ? '0 0 6px 0' : '0 0 12px 0',
                lineHeight: '1.5',
                fontSize: 'inherit',
                color: 'inherit'
              }}>
                {children}
              </p>
            ),
            // 自定义标题样式
            heading: ({ children, level }) => {
              const headingStyle = {
                margin: '16px 0 8px 0',
                color: 'var(--text-primary)',
                fontWeight: 600
              };

              switch (level) {
                case 1: return <h1 style={headingStyle}>{children}</h1>;
                case 2: return <h2 style={headingStyle}>{children}</h2>;
                case 3: return <h3 style={headingStyle}>{children}</h3>;
                case 4: return <h4 style={headingStyle}>{children}</h4>;
                case 5: return <h5 style={headingStyle}>{children}</h5>;
                case 6: return <h6 style={headingStyle}>{children}</h6>;
                default: return <h3 style={headingStyle}>{children}</h3>;
              }
            },
            // 自定义列表样式
            list: ({ children, format }) => {
              const ListTag = format === 'ordered' ? 'ol' : 'ul';
              return (
                <ListTag style={{ margin: '8px 0', paddingLeft: '20px' }}>
                  {children}
                </ListTag>
              );
            },
            // 自定义列表项样式
            'list-item': ({ children }) => (
              <li style={{ margin: '4px 0' }}>
                {children}
              </li>
            ),
            // 自定义引用样式
            quote: ({ children }) => (
              <blockquote style={{
                margin: '16px 0',
                padding: '12px 16px',
                borderLeft: '4px solid var(--color-primary)',
                backgroundColor: 'var(--bg-tertiary)',
                fontStyle: 'italic'
              }}>
                {children}
              </blockquote>
            ),
            // 自定义代码块样式
            code: ({ children }) => (
              <pre style={{
                margin: '16px 0',
                padding: '12px',
                backgroundColor: 'var(--bg-tertiary)',
                borderRadius: '6px',
                overflow: 'auto',
                fontSize: '14px',
                fontFamily: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                color: 'var(--text-primary)'
              }}>
                <code>{children}</code>
              </pre>
            )
          }}
          modifiers={{
            bold: ({ children }) => <strong>{children}</strong>,
            italic: ({ children }) => <em>{children}</em>,
            underline: ({ children }) => <u>{children}</u>,
            strikethrough: ({ children }) => <s>{children}</s>,
            code: ({ children }) => (
              <code style={{
                backgroundColor: 'var(--bg-tertiary)',
                padding: '2px 4px',
                borderRadius: '3px',
                fontSize: '0.9em',
                fontFamily: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                color: 'var(--text-primary)'
              }}>
                {children}
              </code>
            )
          }}
        />
      </div>
    );
  };

  // 处理 Mentor 和 Collaborator 卡片点击事件
  const handleMentorClick = (member: TeamMember) => {
    setSelectedMentor(member);
    setModalVisible(true);
  };

  // 关闭弹窗
  const handleModalClose = () => {
    setModalVisible(false);
    setSelectedMentor(null);
  };

  const getRoleColor = (role: string) => {
    const colors: { [key: string]: string } = {
      'Mentor': '#8e44ad',
      'Collaborator': '#2ecc71',
      'RA': '#f39c12',
      'Alumni': '#e67e22',
      'PhD': '#34c759',
      'Master': '#ff9500',
      'Bachelor': '#5856d6'
    };
    return colors[role] || '#007aff';
  };

  // 获取头像 URL 的辅助函数 - 适配 Strapi v5 结构
  const getAvatarUrl = (member: TeamMember): string | undefined => {
    if (member.avatar && member.avatar.length > 0) {
      const avatarData = member.avatar[0];
      // 优先使用 small 格式，如果没有则使用原图
      const url = avatarData.formats?.small?.url || avatarData.url;
      // 如果是相对路径，添加 API 基础 URL
      if (url.startsWith('/')) {
        return `${import.meta.env.VITE_API_URL || 'http://localhost:1337'}${url}`;
      }
      return url;
    }
    return undefined;
  };

  // 获取不同角色的高度分布
  const getHeightDistribution = (role: string) => {
    switch (role) {
      case 'Alumni':
      case 'RA':
        return { avatar: '60%', research: '20%', contact: '20%', textLines: 3 };
      case 'PhD':
      case 'Master':
      case 'Bachelor':
        return { avatar: '50%', research: '30%', contact: '20%', textLines: 5 };
      case 'Mentor':
      case 'Collaborator':
      default:
        return { avatar: '45%', research: '39%', contact: '16%', textLines: 8 };
    }
  };

  // 渲染成员卡片
  const renderMemberCard = (member: TeamMember) => {
    const heightDist = getHeightDistribution(member.role);

    return (
    <Col xs={24} sm={12} lg={8} xl={6} key={member.id} style={{ marginBottom: '32px' }}>
      <Card
        hoverable={member.role === 'Mentor' || member.role === 'Collaborator'}
        onClick={(member.role === 'Mentor' || member.role === 'Collaborator') ? () => handleMentorClick(member) : undefined}
        style={{
          borderRadius: '20px',
          border: 'none',
          background: 'var(--gradient-secondary)',
          boxShadow: 'var(--shadow-md)',
          height: '512px',
          minHeight: '512px',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          cursor: (member.role === 'Mentor' || member.role === 'Collaborator') ? 'pointer' : 'default',
          transition: 'all 0.3s ease'
        }}
        styles={{
          body: {
            padding: '12px',
            display: 'flex',
            flexDirection: 'column',
            height: '512px',
            flex: 1
          }
        }}
      >
        {/* 头像和基本信息 */}
        <div style={{
          textAlign: 'center',
          marginBottom: '8px',
          height: heightDist.avatar,
          flex: `0 0 ${heightDist.avatar}`,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          overflow: 'hidden'
        }}>
          <div style={{ position: 'relative', display: 'inline-block' }}>
            <Avatar
              size={100}
              src={getAvatarUrl(member)}
              icon={<UserOutlined />}
              style={{
                marginBottom: '16px',
                border: `4px solid ${getRoleColor(member.role)}20`,
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
              }}
            />

          </div>

          <Title level={4} style={{
            margin: '0 0 8px 0',
            color: 'var(--text-primary)',
            fontWeight: '600',
            fontSize: '18px'
          }}>
            {member.name}
          </Title>

          <Paragraph style={{
            color: 'var(--text-secondary)',
            margin: '0 0 12px 0',
            fontSize: '14px'
          }}>
            {member.title}
          </Paragraph>

          {/* Alumni和RA特殊显示：毕业年份和当前职位 */}
          {(member.role === 'Alumni' || member.role === 'RA') && (
            <div style={{ marginBottom: '12px' }}>
              <Paragraph style={{
                color: 'var(--text-secondary)',
                margin: '0 0 4px 0',
                fontSize: '12px'
              }}>
                毕业年份: {member.graduationYear}
              </Paragraph>
              <Paragraph style={{
                color: 'var(--text-secondary)',
                margin: '0 0 4px 0',
                fontSize: '12px',
                fontWeight: '500'
              }}>
                {member.company}
              </Paragraph>
              <Paragraph style={{
                color: 'var(--text-secondary)',
                margin: '0 0 8px 0',
                fontSize: '12px'
              }}>
                {member.position}
              </Paragraph>
            </div>
          )}

          {/* 学生特殊显示：入学年份 */}
          {(member.role === 'PhD' || member.role === 'Master' || member.role === 'Bachelor') && member.enrollmentYear && (
            <div style={{ marginBottom: '12px' }}>
              <Paragraph style={{
                color: 'var(--text-secondary)',
                margin: '0 0 8px 0',
                fontSize: '12px'
              }}>
                入学年份: {member.enrollmentYear}
              </Paragraph>
            </div>
          )}
        </div>

        {/* 研究方向 */}
        <div style={{
          marginBottom: '8px',
          height: heightDist.research,
          flex: `0 0 ${heightDist.research}`,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          position: 'relative' // 为渐变遮罩提供定位上下文
        }}>
          <div style={{
            fontSize: '14px',
            fontWeight: '600',
            color: 'var(--text-primary)',
            marginBottom: '8px',
            display: 'flex',
            alignItems: 'center',
            flex: '0 0 auto'
          }}>
            <ExperimentOutlined style={{ marginRight: '6px', color: getRoleColor(member.role) }} />
            研究方向
          </div>
          <div
            className="research-scrollable"
            style={{
              flex: '1 1 auto',
              overflowY: 'auto',
              overflowX: 'hidden',
              maxHeight: '100%',
              paddingRight: '4px' // 为滚动条留出空间
            }}
          >
            <div style={{
              margin: 0,
              paddingBottom: '12px' // 为渐变遮罩留出空间，避免内容被遮挡
            }}>
              <StrapiBlocksContent
                content={member.researchDirection}
                compact={true}
                style={{
                  fontSize: '13px',
                  color: 'var(--text-secondary)',
                  lineHeight: '1.5'
                }}
              />
            </div>
          </div>
          {/* 滚动提示渐变效果 - 移到外层容器，固定在底部 */}
          <div style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: '4px', // 避免覆盖滚动条
            height: '12px',
            background: 'linear-gradient(transparent, var(--bg-card))',
            pointerEvents: 'none',
            opacity: 0.8,
            zIndex: 1 // 确保在内容之上
          }} />
        </div>

        {/* 联系方式区域 */}
        <div style={{
          height: heightDist.contact,
          flex: `0 0 ${heightDist.contact}`,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '8px 0',
          borderTop: '1px solid var(--border-primary)',
          overflow: 'hidden'
        }}>
          {(member.email || member.phone || member.website) ? (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '6px',
              width: '100%'
            }}>
              {member.email && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '12px',
                  color: 'var(--text-secondary)',
                  maxWidth: '100%',
                  overflow: 'hidden'
                }}>
                  <MailOutlined style={{
                    marginRight: '6px',
                    color: 'var(--text-secondary)',
                    fontSize: '14px'
                  }} />
                  <span style={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {member.email}
                  </span>
                </div>
              )}
              {member.phone && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '12px',
                  color: 'var(--text-secondary)'
                }}>
                  <PhoneOutlined style={{
                    marginRight: '6px',
                    color: 'var(--text-secondary)',
                    fontSize: '14px'
                  }} />
                  <span>{member.phone}</span>
                </div>
              )}
              {member.website && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '12px',
                  color: 'var(--text-secondary)',
                  maxWidth: '100%',
                  overflow: 'hidden'
                }}>
                  <GlobalOutlined style={{
                    marginRight: '6px',
                    color: 'var(--text-secondary)',
                    fontSize: '14px'
                  }} />
                  <a
                    href={member.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      color: 'var(--color-primary)',
                      textDecoration: 'none',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                    onMouseEnter={(e) => {
                      (e.target as HTMLElement).style.textDecoration = 'underline';
                    }}
                    onMouseLeave={(e) => {
                      (e.target as HTMLElement).style.textDecoration = 'none';
                    }}
                  >
                    {member.website.replace(/^https?:\/\//, '')}
                  </a>
                </div>
              )}
            </div>
          ) : (
            // 保持空白但维持固定高度
            <div style={{ height: '100%' }}></div>
          )}
        </div>
      </Card>
    </Col>
    );
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="apple-fade-in" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh',
        flexDirection: 'column'
      }}>
        <Spin
          size="large"
          indicator={<LoadingOutlined style={{ fontSize: 48, color: 'var(--color-primary)' }} spin />}
        />
        <Paragraph style={{
          marginTop: '24px',
          color: 'var(--text-secondary)',
          fontSize: '16px'
        }}>
          正在加载团队成员信息...
        </Paragraph>
      </div>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className="apple-fade-in" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh'
      }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <button
              onClick={() => window.location.reload()}
              style={{
                background: 'var(--color-primary)',
                color: 'var(--text-inverse)',
                border: 'none',
                borderRadius: '6px',
                padding: '8px 16px',
                cursor: 'pointer'
              }}
            >
              重新加载
            </button>
          }
        />
      </div>
    );
  }

  return (
    <div className="apple-fade-in">
      {/* 页面标题 */}
      <div style={{ textAlign: 'center', marginBottom: '64px' }}>
        <Badge
          count={<TeamOutlined style={{ color: 'var(--color-primary)' }} />}
          style={{ backgroundColor: 'transparent' }}
        >
          <Title level={1} style={{
            margin: 0,
            color: 'var(--text-primary)',
            fontWeight: '700',
            fontSize: '3rem',
            letterSpacing: '-0.02em'
          }}>
            团队成员
          </Title>
        </Badge>
        <Paragraph style={{
          fontSize: '20px',
          color: 'var(--text-secondary)',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '600px',
          margin: '20px auto 0'
        }}>
          我们课题组由经验丰富的导师和充满活力的学生组成，致力于在人工智能领域进行前沿研究
        </Paragraph>
      </div>

      {/* 团队成员标签页 */}
      <div style={{ width: '100%' }}>
        <Tabs
          defaultActiveKey="Mentor"
          size="large"
          centered
          style={{
            marginBottom: '40px',
            width: '100%'
          }}
          tabBarStyle={{
            borderBottom: '2px solid var(--border-primary)',
            marginBottom: '40px'
          }}
          items={[
            // 按指定顺序排列标签页：Mentor → Collaborator → RA → Alumni → PhD → Master → Bachelor
            ...['Mentor', 'Collaborator', 'RA', 'Alumni', 'PhD', 'Master', 'Bachelor']
              .filter(role => teamMembers[role] && teamMembers[role].length > 0)
              .map(role => ({
                key: role,
                label: (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    fontSize: '16px',
                    fontWeight: '500',
                    padding: '8px 16px',
                    gap: '8px'
                  }}>
                    {/* 角色指示器 */}
                    <div style={{
                      width: '20px',
                      height: '20px',
                      borderRadius: '6px',
                      background: `linear-gradient(135deg, ${getRoleColor(role)}, ${getRoleColor(role)}80)`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '12px',
                      color: 'white',
                      boxShadow: `0 2px 8px ${getRoleColor(role)}30`
                    }}>
                      <TeamOutlined />
                    </div>
                    <span>{role} ({teamMembers[role].length})</span>
                  </div>
                ),
                children: (
                  <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                    <div style={{ width: '100%', maxWidth: '1200px' }}>
                      <Row gutter={[32, 0]} justify="start">
                        {teamMembers[role].map(renderMemberCard)}
                      </Row>
                    </div>
                  </div>
                )
              }))
          ]}
        />
      </div>

      {/* Mentor 详情弹窗 */}
      <Modal
        title={null}
        open={modalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        centered
        closeIcon={<CloseOutlined style={{ fontSize: '16px', color: 'var(--text-secondary)' }} />}
        styles={{
          body: { padding: 0 },
          content: { borderRadius: '20px', overflow: 'hidden' }
        }}
      >
        {selectedMentor && (
          <div style={{
            background: 'var(--gradient-secondary)',
            minHeight: '600px'
          }}>
            {/* 头部区域 */}
            <div style={{
              background: `linear-gradient(135deg, ${getRoleColor(selectedMentor.role)}15 0%, ${getRoleColor(selectedMentor.role)}05 100%)`,
              padding: '40px 40px 20px',
              textAlign: 'center',
              borderBottom: `1px solid var(--border-primary)`
            }}>
              <Avatar
                size={120}
                src={getAvatarUrl(selectedMentor)}
                icon={<UserOutlined />}
                style={{
                  marginBottom: '20px',
                  border: `4px solid ${getRoleColor(selectedMentor.role)}30`,
                  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)'
                }}
              />
              <Title level={2} style={{
                margin: '0 0 8px 0',
                color: 'var(--text-primary)',
                fontWeight: '600'
              }}>
                {selectedMentor.name}
              </Title>
              <Paragraph style={{
                color: 'var(--text-secondary)',
                margin: '0 0 16px 0',
                fontSize: '16px'
              }}>
                {selectedMentor.title}
              </Paragraph>
            </div>

            {/* 内容区域 */}
            <div style={{ padding: '32px 40px' }}>
              <Space direction="vertical" size={24} style={{ width: '100%' }}>
                {/* 研究方向 */}
                <div>
                  <Title level={4} style={{
                    color: 'var(--text-primary)',
                    marginBottom: '12px',
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <ExperimentOutlined style={{
                      marginRight: '8px',
                      color: getRoleColor(selectedMentor.role)
                    }} />
                    研究方向
                  </Title>
                  <div style={{
                    position: 'relative', // 为渐变遮罩提供定位上下文
                    borderRadius: '12px',
                    background: 'var(--bg-tertiary)'
                  }}>
                    <div
                      className="modal-research-scrollable"
                      style={{
                        margin: 0,
                        fontSize: '15px',
                        color: 'var(--text-primary)',
                        lineHeight: '1.6',
                        padding: '16px',
                        paddingBottom: '36px', // 为渐变遮罩留出空间，避免内容被遮挡
                        borderRadius: '12px',
                        maxHeight: '300px',
                        overflowY: 'auto',
                        overflowX: 'hidden',
                        paddingRight: '24px' // 为滚动条留出空间
                      }}
                    >
                      <div>
                        <StrapiBlocksContent content={selectedMentor.researchDirection} />
                      </div>
                    </div>
                    {/* 滚动提示渐变效果 - 移到外层容器，固定在底部 */}
                    <div style={{
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: '20px',
                      background: 'linear-gradient(transparent, var(--bg-tertiary))',
                      pointerEvents: 'none',
                      opacity: 0.8,
                      borderRadius: '0 0 12px 12px',
                      zIndex: 1 // 确保在内容之上
                    }} />
                  </div>
                </div>

                {/* 教育背景 */}
                {selectedMentor.education && (
                  <div>
                    <Title level={4} style={{
                      color: 'var(--text-primary)',
                      marginBottom: '12px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <BookOutlined style={{
                        marginRight: '8px',
                        color: getRoleColor(selectedMentor.role)
                      }} />
                      教育背景
                    </Title>
                    <div style={{
                      background: 'var(--bg-tertiary)',
                      padding: '16px',
                      borderRadius: '12px'
                    }}>
                      <StrapiBlocksContent content={selectedMentor.education} />
                    </div>
                  </div>
                )}

                {/* 个人简介 */}
                {selectedMentor.bio && (
                  <div>
                    <Title level={4} style={{
                      color: 'var(--text-primary)',
                      marginBottom: '12px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <IdcardOutlined style={{
                        marginRight: '8px',
                        color: getRoleColor(selectedMentor.role)
                      }} />
                      个人简介
                    </Title>
                    <div style={{
                      background: 'var(--bg-tertiary)',
                      padding: '16px',
                      borderRadius: '12px'
                    }}>
                      <StrapiBlocksContent content={selectedMentor.bio} />
                    </div>
                  </div>
                )}

                <Divider style={{ margin: '16px 0' }} />

                {/* 联系方式 */}
                <div>
                  <Title level={4} style={{
                    color: 'var(--text-primary)',
                    marginBottom: '16px'
                  }}>
                    联系方式
                  </Title>
                  <Space direction="vertical" size={12} style={{ width: '100%' }}>
                    {selectedMentor.email && (
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '12px 16px',
                        background: 'var(--bg-tertiary)',
                        borderRadius: '12px'
                      }}>
                        <MailOutlined style={{
                          marginRight: '12px',
                          color: getRoleColor(selectedMentor.role),
                          fontSize: '16px'
                        }} />
                        <a
                          href={`mailto:${selectedMentor.email}`}
                          style={{
                            color: 'var(--color-primary)',
                            textDecoration: 'none',
                            fontSize: '15px'
                          }}
                          onMouseEnter={(e) => {
                            (e.target as HTMLElement).style.textDecoration = 'underline';
                          }}
                          onMouseLeave={(e) => {
                            (e.target as HTMLElement).style.textDecoration = 'none';
                          }}
                        >
                          {selectedMentor.email}
                        </a>
                      </div>
                    )}

                    {selectedMentor.website && (
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '12px 16px',
                        background: 'var(--bg-tertiary)',
                        borderRadius: '12px'
                      }}>
                        <GlobalOutlined style={{
                          marginRight: '12px',
                          color: getRoleColor(selectedMentor.role),
                          fontSize: '16px'
                        }} />
                        <a
                          href={selectedMentor.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{
                            color: 'var(--color-primary)',
                            textDecoration: 'none',
                            fontSize: '15px'
                          }}
                          onMouseEnter={(e) => {
                            (e.target as HTMLElement).style.textDecoration = 'underline';
                          }}
                          onMouseLeave={(e) => {
                            (e.target as HTMLElement).style.textDecoration = 'none';
                          }}
                        >
                          {selectedMentor.website.replace(/^https?:\/\//, '')}
                        </a>
                      </div>
                    )}
                  </Space>
                </div>
              </Space>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Team; 